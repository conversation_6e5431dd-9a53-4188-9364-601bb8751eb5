import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../models/seller.dart';
import '../../providers/seller_provider.dart';

import '../../utils/toast_utils.dart';
import '../../providers/nickname_provider.dart';
import '../../providers/unified_workspace_provider.dart';

import '../../widgets/confirmation_dialog.dart';
import '../../widgets/app_bar_styles.dart';
import '../../utils/subscription_utils.dart';
import '../subscription/subscription_plans_screen.dart';

class SellerManagementScreen extends ConsumerStatefulWidget {
  const SellerManagementScreen({super.key});

  @override
  ConsumerState<SellerManagementScreen> createState() =>
      _SellerManagementScreenState();
}

class _SellerManagementScreenState extends ConsumerState<SellerManagementScreen>
    with RestorationMixin {
  final TextEditingController _sellerNameController = TextEditingController();

  @override
  String? get restorationId => 'seller_management_screen';
  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {}

  @override
  void initState() {
    super.initState();
    // 화면 진입 시 한 번만 데이터 로드
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadInitialData();
      }
    });
  }

  /// 초기 데이터 로드 (한 번만 실행)
  Future<void> _loadInitialData() async {
    if (!mounted) return;

    try {
      await ref.read(sellerNotifierProvider.notifier).loadSellers();
    } catch (e) {
      // 오류 발생 시 무시 (이미 Provider에서 처리됨)
    }
  }

  @override
  void dispose() {
    _sellerNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final sellerAsync = ref.watch(sellerNotifierProvider);

    // 판매자별 관리 기능 접근 권한 확인
    return Consumer(
      builder: (context, ref, child) {
        return FutureBuilder<bool>(
          future: SubscriptionUtils.checkFeatureAccess(
            ref: ref,
            context: context,
            featureName: 'sellerManagement',
            showDialog: false,
          ),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Scaffold(
                appBar: AppBar(
                  title: Builder(builder: (ctx)=> Text('판매자 관리', style: AppBarStyles.of(ctx))),
                  centerTitle: true,
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                ),
                body: const Center(child: CircularProgressIndicator()),
              );
            }

            final hasAccess = snapshot.data ?? false;

            if (!hasAccess) {
              return Scaffold(
                appBar: AppBar(
                  title: Builder(builder: (ctx)=> Text('판매자 관리', style: AppBarStyles.of(ctx))),
                  centerTitle: true,
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                ),
                body: SafeArea(
                  child: _buildRestrictedContent(),
                ),
              );
            }

            // 🔥 홈 대시보드에서 이미 권한 체크를 했으므로 여기서는 중복 체크 제거
            return Scaffold(
              appBar: AppBar(
                title: Builder(builder: (ctx)=> Text('판매자 관리', style: AppBarStyles.of(ctx))),
                centerTitle: true,
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
              ),
              body: SafeArea(
                child: _buildBody(sellerAsync),
              ),
              floatingActionButton: FloatingActionButton(
                onPressed: _navigateToAddSellerScreen,
                child: const Icon(Icons.add),
                tooltip: '판매자 추가',
              ),
            );
          },
        );
      },
    );
  }

  /// 기능 제한 안내 화면
  Widget _buildRestrictedContent() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.star,
              size: 80,
              color: Colors.amber,
            ),
            SizedBox(height: 24),
            Text(
              '플러스 플랜 필요',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            Text(
              '판매자별 관리 기능은 플러스 플랜에서만 사용할 수 있습니다.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const SubscriptionPlansScreen(),
                  ),
                );
              },
              icon: Icon(Icons.upgrade),
              label: Text('프로 플랜으로 업그레이드'),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBody(SellerState sellerAsync) {
    if (sellerAsync.isLoading) {
      return _buildLoadingState();
    } else if (sellerAsync.hasError) {
      return _buildErrorState(sellerAsync.errorMessage ?? 'Unknown error');
    } else {
      return _buildContent(sellerAsync);
    }
  }

  // 판매자 추가 화면으로 이동 (예시)
  Future<void> _navigateToAddSellerScreen() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => _buildAddSellerDialog(),
    );
    if (result == true) {
      ref.read(sellerNotifierProvider.notifier).loadSellers();
    }
  }

  // 판매자 추가 다이얼로그
  Widget _buildAddSellerDialog() {
    return AlertDialog(
      title: const Text('판매자 추가'),
      content: TextField(
        controller: _sellerNameController,
        decoration: const InputDecoration(hintText: '판매자 이름 입력'),
        autofocus: true,
        textInputAction: TextInputAction.done,
        onSubmitted: (_) => _onAddSellerDialogSubmit(),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: const Text('취소'),
        ),
        ElevatedButton(
          onPressed: _onAddSellerDialogSubmit,
          child: const Text('추가'),
        ),
      ],
    );
  }

  Future<void> _onAddSellerDialogSubmit() async {
    final sellerName = _sellerNameController.text.trim();
    if (sellerName.isEmpty) {
      _showMessage('판매자 이름을 입력해주세요.');
      return;
    }
    
    final sellerState = ref.read(sellerNotifierProvider);
    final nickname = ref.read(nicknameProvider);
    
    // 중복 검사
    final isDuplicate = sellerState.sellers.any(
      (seller) => seller.name.toLowerCase() == sellerName.toLowerCase(),
    );
    if (isDuplicate) {
      _showMessage('\'$sellerName\' 판매자는 이미 존재합니다.');
      return;
    }
    
    // 닉네임과 중복 검사
    if (nickname != null && sellerName.toLowerCase() == nickname.name.toLowerCase()) {
      _showMessage('\'$sellerName\'은 로그인된 사용자의 닉네임과 동일합니다. 다른 이름을 사용해주세요.');
      return;
    }
    
    // 현재 워크스페이스 확인
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      _showMessage('현재 선택된 행사가 없습니다. 행사를 선택해주세요.');
      return;
    }
    
    try {
      final newSeller = Seller.create(name: sellerName, eventId: currentWorkspace.id);
      await ref.read(sellerNotifierProvider.notifier).addSeller(newSeller);
      _sellerNameController.clear();
      _showMessage('\'$sellerName\' 판매자가 추가되었습니다.');
      Navigator.of(context).pop(true); // 반드시 true 반환
    } catch (e) {
      _showMessage('판매자 추가 중 오류가 발생했습니다: $e');
    }
  }

  /// 삭제 확인 다이얼로그
  Future<void> _showDeleteConfirmDialog(Seller seller) async {
    final confirmed = await ConfirmationDialog.showDelete(
      context: context,
      title: '판매자 삭제',
      message: '\'${seller.name}\' 판매자를 삭제하시겠습니까?',
      onConfirm: () async {
        try {
          await ref.read(sellerNotifierProvider.notifier).deleteSeller(seller.id!);
          _showMessage('\'${seller.name}\' 판매자가 삭제되었습니다.');
        } catch (e) {
          _showMessage('판매자 삭제 중 오류가 발생했습니다.');
        }
      },
    );
    if (confirmed == true) {
      ref.read(sellerNotifierProvider.notifier).loadSellers();
    }
  }



  /// 메시지 표시
  void _showMessage(String message) {
    ToastUtils.showMessage(context, message);
  }

  /// 판매자 순서 변경
  Future<void> _reorderSellers(int oldIndex, int newIndex, List<Seller> sellers) async {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    try {
      // 순서 변경을 위한 업데이트 목록 생성
      final updates = <Map<String, dynamic>>[];

      // 이동할 판매자
      final movedSeller = sellers[oldIndex];

      if (oldIndex < newIndex) {
        // 아래로 이동: oldIndex+1부터 newIndex까지의 판매자들을 위로 이동
        for (int i = oldIndex + 1; i <= newIndex; i++) {
          updates.add({
            'id': sellers[i].id,
            'sortOrder': i - 1,
          });
        }
      } else {
        // 위로 이동: newIndex부터 oldIndex-1까지의 판매자들을 아래로 이동
        for (int i = newIndex; i < oldIndex; i++) {
          updates.add({
            'id': sellers[i].id,
            'sortOrder': i + 1,
          });
        }
      }

      // 이동할 판매자의 새 순서 설정
      updates.add({
        'id': movedSeller.id,
        'sortOrder': newIndex,
      });

      // 데이터베이스에 순서 변경 저장
      await ref.read(sellerRepositoryProvider).updateMultipleSellerOrders(updates);

      // 판매자 목록 새로고침
      await ref.read(sellerNotifierProvider.notifier).loadSellers();

      _showMessage('판매자 순서가 변경되었습니다.');
    } catch (e) {
      _showMessage('순서 변경 중 오류가 발생했습니다: $e');
    }
  }

  /// 판매자 수정 다이얼로그
  Future<void> _showEditSellerDialog(Seller seller) async {
    final controller = TextEditingController(text: seller.name);

    try {
      final result = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text(
            '판매자 이름 수정',
            style: TextStyle(fontFamily: 'Pretendard'),
          ),
          content: TextField(
            controller: controller,
            decoration: const InputDecoration(hintText: '새 판매자 이름 입력'),
            autofocus: true,
            textInputAction: TextInputAction.done,
            style: const TextStyle(fontFamily: 'Pretendard'),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                '취소',
                style: TextStyle(fontFamily: 'Pretendard'),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(controller.text.trim()),
              child: const Text(
                '수정',
                style: TextStyle(fontFamily: 'Pretendard'),
              ),
            ),
          ],
        ),
      );

      if (result != null && result.isNotEmpty && result != seller.name) {
        try {
          await ref.read(sellerNotifierProvider.notifier).updateSeller(
            seller.copyWith(name: result),
          );
          _showMessage('판매자 이름이 수정되었습니다.');
        } catch (e) {
          _showMessage('판매자 이름 수정 중 오류가 발생했습니다.');
        }
      }
    } finally {
      // 다이얼로그가 완전히 닫힌 후에 컨트롤러를 안전하게 dispose
      controller.dispose();
    }
  }

  Widget _buildContent(SellerState sellerAsync) {
    final nickname = ref.watch(nicknameProvider);
    return ReorderableListView.builder(
      itemCount: sellerAsync.sellers.length,
      onReorder: (oldIndex, newIndex) => _reorderSellers(oldIndex, newIndex, sellerAsync.sellers),
      itemBuilder: (context, index) {
        final seller = sellerAsync.sellers[index];
        // 닉네임과 이름이 같고 대표 판매자인 경우 닉네임 기반 판매자로 판단
        final isLinkedToNickname = nickname != null && 
                                 seller.name == nickname.name && 
                                 seller.isDefault;
        String? subtitleText;
        Color? subtitleColor;
        bool canModify = true;

        if (isLinkedToNickname) {
          subtitleText = '로그인된 판매자  ( 수정/삭제 불가 )';
          subtitleColor = Theme.of(context).colorScheme.primary;
          canModify = false;
        }

        return ListTile(
          key: ValueKey('seller_${seller.id ?? index}'),
          title: Text(seller.name),
          subtitle: subtitleText != null
              ? Text(subtitleText, style: TextStyle(color: subtitleColor))
              : null,
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.edit),
                onPressed: canModify ? () => _showEditSellerDialog(seller) : null,
                tooltip: !canModify
                    ? '닉네임과 연결된 판매자는 수정할 수 없습니다'
                    : '수정',
              ),
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: canModify ? () => _showDeleteConfirmDialog(seller) : null,
                tooltip: !canModify
                    ? '닉네임과 연결된 판매자는 삭제할 수 없습니다'
                    : '삭제',
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Text(
        '판매자 목록을 불러오는 중 오류가 발생했습니다.\n$error',
        textAlign: TextAlign.center,
        style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', color: Colors.red),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(child: CircularProgressIndicator());
  }
}


