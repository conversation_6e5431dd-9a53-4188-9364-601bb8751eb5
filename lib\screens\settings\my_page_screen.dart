import 'dart:io';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;
import 'package:google_sign_in/google_sign_in.dart';

import '../../providers/nickname_provider.dart';

import '../../widgets/profile_avatar_widget.dart';
import '../../widgets/image_crop_widget.dart';
import 'package:path_provider/path_provider.dart';
import '../../utils/logger_utils.dart';

import '../../utils/app_colors.dart';
import '../../utils/image_utils.dart';
import '../../utils/toast_utils.dart';
import '../../utils/logout_manager.dart';


import '../../widgets/app_bar_styles.dart';

import '../../providers/subscription_provider.dart';
import '../subscription/subscription_plans_screen.dart';
import '../subscription/subscription_screen.dart';
import '../../models/subscription_plan.dart';
import 'faq_screen.dart';
import 'native_phone_verification_screen.dart';


class MyPageScreen extends ConsumerStatefulWidget {
  const MyPageScreen({super.key});

  @override
  ConsumerState<MyPageScreen> createState() => _MyPageScreenState();
}

class _MyPageScreenState extends ConsumerState<MyPageScreen> {
  bool _loadingImage = false;

  /// 암호화된 nonce 생성 (Apple Sign-In용)
  String _generateNonce([int length = 32]) {
    const charset = '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
    final random = Random.secure();
    return List.generate(length, (_) => charset[random.nextInt(charset.length)]).join();
  }

  /// SHA256 해시 생성
  String _sha256ofString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  String? _userEmail;
  String? _userPhone;
  bool _phoneVerified = false;

  @override
  void initState() {
    super.initState();
    _loadProfileImage();
    _loadUserSettings();
    _loadUserEmail();
    _refreshUserData();
  }

  /// 사용자 설정 로드
  Future<void> _loadUserSettings() async {
    try {
      // 실시간 동기화 설정은 이제 행사별로 관리되므로 여기서는 특별히 로드할 설정이 없음
      // 필요시 다른 사용자 설정을 여기서 로드할 수 있음
    } catch (e) {
      LoggerUtils.logError('사용자 설정 로드 실패', tag: 'MyPageScreen', error: e);
    }
  }

  /// 사용자 데이터 새로고침 (전화번호 인증 상태 포함)
  Future<void> _refreshUserData() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user.uid)
            .get();

        if (userDoc.exists && mounted) {
          final data = userDoc.data()!;
          setState(() {
            _userPhone = data['phone'] as String?;
            _phoneVerified = data['phoneVerified'] as bool? ?? false;
          });
        }
      }
    } catch (e) {
      LoggerUtils.logError('사용자 데이터 새로고침 실패', tag: 'MyPageScreen', error: e);
    }
  }

  /// Firestore에서 사용자 이메일 로드
  Future<void> _loadUserEmail() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // 먼저 Firebase Auth에서 이메일 확인
        String? email = user.email;

        // Firebase Auth에서 이메일이 없으면 Firestore에서 가져오기
        if (email == null || email.isEmpty) {
          final doc = await FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .get();

          if (doc.exists && doc.data() != null) {
            email = doc.data()!['email'] as String?;
          }
        }

        // 이메일이 여전히 없으면 계정 유효성 검증
        if (email == null || email.isEmpty) {
          LoggerUtils.logWarning('사용자 이메일을 찾을 수 없음 - 계정 유효성 검증 시작', tag: 'MyPageScreen');
          await _validateAccountAndHandleInvalid();
          return;
        }

        if (mounted) {
          setState(() {
            _userEmail = email;
          });
        }
      }
    } catch (e) {
      LoggerUtils.logError('사용자 이메일 로드 실패', tag: 'MyPageScreen', error: e);
      // 오류 발생 시에도 계정 유효성 검증
      await _validateAccountAndHandleInvalid();
    }
  }

  /// 계정 유효성 검증 및 무효한 계정 처리
  Future<void> _validateAccountAndHandleInvalid() async {
    try {
      // 로컬 전용 모드: 계정 검증 항상 성공
      LoggerUtils.logInfo('로컬 전용 모드: 계정 검증 완료', tag: 'MyPageScreen');

      // 이메일 정보 설정
      if (mounted) {
        setState(() {
          _userEmail = '이메일 정보 없음';
        });
      }
    } catch (e) {
      LoggerUtils.logError('계정 유효성 검증 실패', tag: 'MyPageScreen', error: e);
      // 검증 실패 시 이메일 없음으로 표시
      if (mounted) {
        setState(() {
          _userEmail = '이메일 확인 불가';
        });
      }
    }
  }







  /// 플랜별 이름
  String _getPlanName(SubscriptionPlanType planType) {
    switch (planType) {
      case SubscriptionPlanType.free:
        return '사용중인 플랜: 무료 플랜';
      case SubscriptionPlanType.plus:
        return '사용중인 플랜: 플러스 플랜';
      // 프로 플랜 제거됨
    }
  }

  /// 플랜별 부제목
  String _getPlanSubtitle(SubscriptionPlanType planType) {
    switch (planType) {
      case SubscriptionPlanType.free:
        return '기본 기능만 사용 가능';
      case SubscriptionPlanType.plus:
        return '행사, 상품 무제한 등록, 프리미엄 기능 사용 가능';
      // 프로 플랜 제거됨
    }
    
  }

  /// 계정 관리 다이얼로그
  void _showAccountManagement() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('계정 관리'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(
                _phoneVerified ? Icons.verified : Icons.phone_android,
                color: _phoneVerified ? Colors.green : null,
              ),
              title: Text(_phoneVerified ? '번호인증 완료' : '전화번호 인증'),
              subtitle: _phoneVerified && _userPhone != null
                  ? Text(_userPhone!)
                  : const Text('본인확인 번호등록'),
              trailing: _phoneVerified
                  ? const Icon(Icons.check_circle, color: Colors.green)
                  : const Icon(Icons.arrow_forward_ios),
              onTap: _phoneVerified ? null : () {
                Navigator.of(context).pop();
                _navigateToPhoneVerification();
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.lock),
              title: const Text('비밀번호 변경'),
              onTap: () {
                Navigator.of(context).pop();
                _showChangePasswordDialog();
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.logout, color: Colors.orange),
              title: const Text('로그아웃', style: TextStyle(color: Colors.orange)),
              onTap: () {
                Navigator.of(context).pop();
                _logout();
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete_forever, color: Colors.red),
              title: const Text('회원탈퇴', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.of(context).pop();
                _deleteAccount();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('닫기'),
          ),
        ],
      ),
    );
  }
  /// 전화번호 인증 화면으로 이동
  Future<void> _navigateToPhoneVerification() async {
    try {
      final result = await Navigator.of(context).push<bool>(
        MaterialPageRoute(
          builder: (context) => const NativePhoneVerificationScreen(),
        ),
      );

      // 인증 성공 시 UI 새로고침 및 실시간 상태 확인
      if (result == true && mounted) {
        // 잠시 대기 후 Firestore에서 최신 상태 확인
        await Future.delayed(const Duration(milliseconds: 500));
        await _refreshUserData();

        setState(() {});
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('전화번호 인증이 완료되었습니다.'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      LoggerUtils.logError('전화번호 인증 화면 이동 실패', tag: 'MyPageScreen', error: e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('전화번호 인증 화면을 열 수 없습니다.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 구독 관리 페이지로 이동 (전화번호 인증 확인)
  Future<void> _navigateToSubscriptionManagement() async {
    try {
      // 현재 사용자의 전화번호 인증 상태 확인
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('로그인이 필요합니다.'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Firestore에서 전화번호 인증 상태 확인
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      final phoneVerified = userDoc.data()?['phoneVerified'] as bool? ?? false;

      if (!phoneVerified) {
        // 전화번호 인증이 안 된 경우 안내 다이얼로그 표시
        _showPhoneVerificationRequiredDialog();
        return;
      }

      // 임시로 전화번호 인증 확인 없이 바로 구독 관리 페이지로 이동
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const SubscriptionScreen(),
        ),
      );
    } catch (e) {
      LoggerUtils.logError('구독 관리 페이지 이동 실패', tag: 'MyPageScreen', error: e);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('구독 관리 페이지를 열 수 없습니다.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 전화번호 인증 필요 다이얼로그
  void _showPhoneVerificationRequiredDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('전화번호 인증 필요'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('구독 서비스 이용을 위해서는 전화번호 인증이 필요합니다.'),
            SizedBox(height: 12),
            Text('• 안전한 결제 및 서비스 제공'),
            Text('• 계정 보안 강화'),
            Text('• 고객 지원 서비스 향상'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToPhoneVerification();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primarySeed,
              foregroundColor: Colors.white,
            ),
            child: const Text('전화번호 인증하기'),
          ),
        ],
      ),
    );
  }







  Future<void> _loadProfileImage() async {
    setState(() => _loadingImage = true);
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        // 프로필 이미지 로딩 로직 - ProfileAvatarWidget이 이미 로컬 파일을 잘 처리하므로
        // Firebase Storage 직접 접근은 불필요함 (에러 방지)
        final nickname = ref.read(nicknameProvider);
        if (nickname?.profileImagePath != null &&
            nickname!.profileImagePath!.isNotEmpty) {
          final localFile = File(nickname.profileImagePath!);
          if (await localFile.exists()) {
            LoggerUtils.logInfo('로컬 프로필 이미지 발견', tag: 'MyPageScreen');
            setState(() => _loadingImage = false);
            return;
          }
        }

        // Firebase Storage 직접 접근 제거 - NicknameProvider가 이미 처리함
        LoggerUtils.logInfo('프로필 이미지 로드 완료 (로컬 파일 없음)', tag: 'MyPageScreen');
      }
    } catch (e) {
      LoggerUtils.logError('프로필 이미지 로딩 실패', tag: 'MyPageScreen', error: e);
    } finally {
      if (mounted) setState(() => _loadingImage = false);
    }
  }



  Future<void> _pickAndUploadProfileImage() async {
    final picker = ImagePicker();
    final picked = await picker.pickImage(source: ImageSource.gallery, imageQuality: 85);
    if (picked == null) return;
    
    // 1. 3단계 방식: 흰색 800x800 캔버스 + 이미지 최대 650px로 전처리
    final originalBytes = await picked.readAsBytes();
    final paddedBytes = await addWhitePaddingAndCenterImage(originalBytes);
    final tempDir = await Directory.systemTemp.createTemp('profile_crop_temp');
    final tempFile = File('${tempDir.path}/padded_${DateTime.now().millisecondsSinceEpoch}.jpg');
    await tempFile.writeAsBytes(paddedBytes);
    
    // 2. 크롭 다이얼로그 호출 (흰색 패딩 포함 이미지)
    final cropped = await ImageCropUtils.cropImage(
      context: context,
      imagePath: tempFile.path,
      shape: CropShape.circle,
      aspectRatio: 1.0,
    );
    if (cropped == null) return;
    
    // 3. 크롭된 이미지를 200x200으로 리사이즈만 함 (addWhitePaddingAndCenterImage 호출 X)
    final croppedBytes = await cropped.readAsBytes();
    final img.Image? imgDecoded = img.decodeImage(croppedBytes);
    final img.Image imgResized = img.copyResize(imgDecoded!, width: 200, height: 200);
    final jpgBytes = img.encodeJpg(imgResized, quality: 80);
    final finalFile = File('${tempDir.path}/final_${DateTime.now().millisecondsSinceEpoch}.jpg');
    await finalFile.writeAsBytes(jpgBytes);
    
    setState(() {
      _loadingImage = true;
    });
    
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        LoggerUtils.logError(
          '로그인된 사용자가 없습니다',
          tag: 'MyPageScreen',
        );
        return;
      }

      // 현재 구독 플랜 확인
      final subscriptionService = ref.read(subscriptionServiceProvider);
      final currentPlanType = await subscriptionService.getCurrentPlanType();
      final isProUser = currentPlanType == SubscriptionPlanType.plus; // 프로 플랜 제거됨

      LoggerUtils.logDebug(
        '로그인된 사용자: ${user.email}, UID: ${user.uid}, 플랜: $currentPlanType',
        tag: 'MyPageScreen',
      );

      String? downloadUrl;

      // 로컬 전용 모드: Firebase 업로드 제거
      LoggerUtils.logInfo(
        '로컬 전용 모드: 로컬에만 저장',
        tag: 'MyPageScreen',
      );

      // 로컬에 프로필 이미지 저장 (더 안전한 방식)
      final dir = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final localFile = File('${dir.path}/profile_${user.uid}_$timestamp.jpg');

      LoggerUtils.logDebug(
        '로컬 파일 저장 시도: ${localFile.path}',
        tag: 'MyPageScreen',
      );
      LoggerUtils.logDebug(
        '저장할 데이터 크기: ${jpgBytes.length} bytes',
        tag: 'MyPageScreen',
      );
      LoggerUtils.logDebug(
        '디렉토리 경로: ${dir.path}',
        tag: 'MyPageScreen',
      );

      // 파일 저장 전 디렉토리 확인 및 생성
      if (!await dir.exists()) {
        await dir.create(recursive: true);
        LoggerUtils.logDebug(
          '디렉토리 생성: ${dir.path}',
          tag: 'MyPageScreen',
        );
      }

      // 디렉토리 권한 확인
      final dirStat = await dir.stat();
      LoggerUtils.logDebug(
        '디렉토리 권한: ${dirStat.mode}',
        tag: 'MyPageScreen',
      );

      // 파일 저장 (동기화 강제)
      await localFile.writeAsBytes(jpgBytes, flush: true);

      // 파일 시스템 동기화를 위한 대기
      await Future.delayed(const Duration(milliseconds: 200));

      // 파일 존재 및 데이터 무결성 확인 (여러 번 시도)
      bool fileVerified = false;
      for (int attempt = 1; attempt <= 3; attempt++) {
        LoggerUtils.logDebug(
          '파일 검증 시도 $attempt/3',
          tag: 'MyPageScreen',
        );

        if (await localFile.exists()) {
          final fileSize = await localFile.length();
          LoggerUtils.logDebug(
            '파일 크기: $fileSize bytes (예상: ${jpgBytes.length} bytes)',
            tag: 'MyPageScreen',
          );

          if (fileSize > 0 && fileSize == jpgBytes.length) {
            // 실제 이미지 데이터 검증
            try {
              final savedBytes = await localFile.readAsBytes();
              if (savedBytes.length == jpgBytes.length) {
                LoggerUtils.logDebug(
                  '로컬 파일 저장 및 검증 완료: ${localFile.path} (크기: $fileSize bytes)',
                  tag: 'MyPageScreen',
                );
                fileVerified = true;
                break;
              } else {
                LoggerUtils.logError(
                  '저장된 파일 크기 불일치: ${savedBytes.length} != ${jpgBytes.length}',
                  tag: 'MyPageScreen',
                );
              }
            } catch (e) {
              LoggerUtils.logError(
                '저장된 파일 읽기 실패 (시도 $attempt)',
                error: e,
                tag: 'MyPageScreen',
              );
            }
          } else {
            LoggerUtils.logError(
              '파일 크기 불일치 (시도 $attempt): $fileSize != ${jpgBytes.length}',
              tag: 'MyPageScreen',
            );
          }
        } else {
          LoggerUtils.logError(
            '파일 존재하지 않음 (시도 $attempt): ${localFile.path}',
            tag: 'MyPageScreen',
          );
        }

        if (attempt < 3) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }

      if (!fileVerified) {
        throw Exception('로컬 파일 저장 검증 실패 (3회 시도 후)');
      }

      // 기존 프로필 이미지 파일들 정리 (수정시간 기준 최신만 유지, 현재 저장한 파일은 무조건 보존)
      final files = dir
          .listSync()
          .whereType<File>()
          .where((f) =>
              (f.path.contains('profile_image_${user.uid}_') || f.path.contains('profile_${user.uid}_')) &&
              f.path.endsWith('.jpg'))
          .toList();
      // 수정 시간 기준으로 내림차순 정렬
      files.sort((a, b) => b.lastModifiedSync().compareTo(a.lastModifiedSync()));
      // 첫 번째(최신) 외에는 삭제하되, 방금 저장한 파일은 절대 삭제하지 않음
      for (int i = 1; i < files.length; i++) {
        final f = files[i];
        if (f.path == localFile.path) continue;
        try { await f.delete(); } catch (_) {}
      }

      // NicknameProvider 상태 즉시 업데이트
      await ref.read(nicknameProvider.notifier).updateProfileImage(localFile.path, downloadUrl ?? '');

      // Flutter의 이미지 캐시 클리어 (프로필 이미지 즉시 반영을 위해)
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();

      LoggerUtils.logDebug(
        '프로필 이미지 저장 완료: ${localFile.path}${isProUser ? ' (서버 동기화 포함)' : ' (로컬만)'}',
        tag: 'MyPageScreen',
      );

      // 강제 리빌드를 위한 상태 업데이트
      if (mounted) {
        setState(() {
          _loadingImage = false;
        });

        // 약간의 지연 후 다시 한번 상태 업데이트 (위젯 리빌드 보장)
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            setState(() {});
          }
        });

        ToastUtils.showSuccess(
          context,
          '프로필 이미지가 변경되었습니다.',
        );
      }

    } catch (e, stackTrace) {
      LoggerUtils.logError(
        '프로필 이미지 처리 실패: $e',
        tag: 'MyPageScreen',
        error: e,
        stackTrace: stackTrace,
      );
      if (mounted) {
        ToastUtils.showError(context, '프로필 이미지 변경에 실패했습니다: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _loadingImage = false);
      }
      // 임시 파일들 정리
      try {
        await tempFile.delete();
        await tempDir.delete();
        await finalFile.delete();
      } catch (e) {
        LoggerUtils.logWarning('임시 파일 정리 실패: $e', tag: 'MyPageScreen');
      }
    }
  }

  Future<void> _showChangePasswordDialog() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null || user.email == null) return;
    final ok = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('비밀번호 재설정'),
        content: Text('${user.email}로 비밀번호 재설정 메일을 발송할까요?'),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context, false), child: const Text('취소')),
          ElevatedButton(onPressed: () => Navigator.pop(context, true), child: const Text('발송')),
        ],
      ),
    );
    if (ok == true) {
      try {
        await FirebaseAuth.instance.sendPasswordResetEmail(email: user.email!);
        ToastUtils.showSuccess(context, '비밀번호 재설정 메일이 발송되었습니다. 이메일을 확인하세요.');
      } catch (e) {
        ToastUtils.showError(context, '메일 발송 실패: $e');
      }
    }
  }



  /// 새로운 로그아웃 - Phoenix를 사용한 완전한 앱 재시작
  Future<void> _logout() async {
    await LogoutManager.performCompleteLogout(
      context: context,
    );
  }

  /// 사용자의 로그인 방법 확인
  String _getUserAuthProvider(User user) {
    for (final providerData in user.providerData) {
      switch (providerData.providerId) {
        case 'google.com':
          return 'google';
        case 'apple.com':
          return 'apple';
        case 'password':
          return 'email';
      }
    }
    return 'unknown';
  }

  /// 로그인 방법에 따른 재인증
  Future<void> _reauthenticateUser(User user, String authProvider) async {
    switch (authProvider) {
      case 'email':
        // 이메일/비밀번호 재인증
        final password = await _showPasswordDialog();
        if (password == null) throw Exception('비밀번호가 필요합니다.');

        final email = user.email;
        if (email == null) throw Exception('이메일 정보를 찾을 수 없습니다.');

        final cred = EmailAuthProvider.credential(email: email, password: password);
        await user.reauthenticateWithCredential(cred);
        break;

      case 'google':
        // 구글 재인증
        final GoogleSignIn googleSignIn = GoogleSignIn.instance;
        // google-services.json에서 자동으로 설정을 가져오도록 수정
        try {
          await googleSignIn.initialize();
          LoggerUtils.logInfo('Google Sign-In 초기화 완료 (재인증)', tag: 'MyPageScreen');
        } catch (e) {
          LoggerUtils.logError('Google Sign-In 초기화 실패 (재인증)', tag: 'MyPageScreen', error: e);
          throw Exception('Google 서비스 초기화에 실패했습니다.');
        }
        final GoogleSignInAccount? googleUser = await googleSignIn.authenticate();
        if (googleUser == null) throw Exception('구글 재인증이 취소되었습니다.');

        final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
        final credential = GoogleAuthProvider.credential(idToken: googleAuth.idToken);
        await user.reauthenticateWithCredential(credential);
        break;

      case 'apple':
        if (Platform.isAndroid) {
          // Android: Firebase 공식 문서 권장 방식 - OAuthProvider를 이용한 재인증
          final provider = OAuthProvider('apple.com');
          provider.addScope('email');
          provider.addScope('name');
          await user.reauthenticateWithProvider(provider);
        } else {
          // iOS: 네이티브 Apple Sign-In 사용
          final rawNonce = _generateNonce();
          final nonce = _sha256ofString(rawNonce);

          final appleCredential = await SignInWithApple.getAppleIDCredential(
            scopes: [
              AppleIDAuthorizationScopes.email,
              AppleIDAuthorizationScopes.fullName,
            ],
            nonce: nonce,
          );

          final oauthCredential = OAuthProvider('apple.com').credential(
            idToken: appleCredential.identityToken,
            rawNonce: rawNonce,
          );

          await user.reauthenticateWithCredential(oauthCredential);
        }
        break;

      default:
        throw Exception('지원하지 않는 로그인 방법입니다.');
    }
  }



  /// 단순화된 안전한 회원탈퇴 - Phoenix로 완전 재시작
  Future<void> _deleteAccount() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    try {
      // 1. 회원탈퇴 확인
      final confirmed = await _showDeleteConfirmDialog();
      if (!confirmed) return;

      // 2. 사용자 로그인 방법 확인
      final authProvider = _getUserAuthProvider(user);
      LoggerUtils.logInfo('회원탈퇴 시작 - 로그인 방법: $authProvider', tag: 'MyPageScreen');

      // 3. 로딩 다이얼로그 표시
      if (!mounted) return;
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('회원탈퇴 처리 중...'),
            ],
          ),
        ),
      );

      // 4. 로그인 방법에 따른 재인증
      await _reauthenticateUser(user, authProvider);

      // 5. Firebase Functions 호출 (서버 데이터 삭제)
      final callable = FirebaseFunctions.instance.httpsCallable('deleteUserData');
      final result = await callable.call();

      if (result.data['success'] != true) {
        throw Exception('서버 데이터 삭제 실패');
      }

      // 6. 로컬 데이터 삭제 (Provider 정리는 Phoenix가 처리)
      await _clearAllLocalDataSafely();

      // 6.5. Firebase 사용자 설정도 삭제 (회원탈퇴는 서버에서 처리되지만 혹시 모를 경우 대비)
      await _clearFirebaseUserSettingsForDeletion();

      // 7. Firebase Auth 로그아웃 (가장 마지막에)
      await FirebaseAuth.instance.signOut();

      // 8. 완료 메시지 후 앱 완전 재시작
      if (mounted) {
        Navigator.of(context).pop(); // 로딩 다이얼로그 닫기
        await _showCompletionDialog();
      }

    } catch (e) {
      // 오류 처리
      if (mounted) {
        Navigator.of(context).pop(); // 로딩 다이얼로그 닫기
        ToastUtils.showError(context, '회원탈퇴 실패: ${e.toString()}');
      }
    }
  }

  /// 안전한 로컬 데이터 완전 삭제
  Future<void> _clearAllLocalDataSafely() async {
    try {
      LoggerUtils.logInfo('안전한 로컬 데이터 삭제 시작', tag: 'MyPageScreen');

      // 1. SharedPreferences 삭제
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.clear();
        LoggerUtils.logInfo('SharedPreferences 삭제 완료', tag: 'MyPageScreen');
      } catch (e) {
        LoggerUtils.logError('SharedPreferences 삭제 실패: $e', tag: 'MyPageScreen');
      }

      // 2. SQLite 데이터베이스 삭제
      try {
        final databasesPath = await getDatabasesPath();
        final dbPath = '$databasesPath/parabara_database.db';
        await databaseFactory.deleteDatabase(dbPath);
        LoggerUtils.logInfo('SQLite 데이터베이스 삭제 완료: $dbPath', tag: 'MyPageScreen');
      } catch (e) {
        LoggerUtils.logError('SQLite 데이터베이스 삭제 실패: $e', tag: 'MyPageScreen');
      }

      // 3. 애플리케이션 파일 삭제
      try {
        final appDir = await getApplicationDocumentsDirectory();
        if (await appDir.exists()) {
          await appDir.delete(recursive: true);
          LoggerUtils.logInfo('애플리케이션 파일 삭제 완료', tag: 'MyPageScreen');
        }
      } catch (e) {
        LoggerUtils.logError('애플리케이션 파일 삭제 실패: $e', tag: 'MyPageScreen');
      }

      // 4. 임시 파일 삭제
      try {
        final tempDir = await getTemporaryDirectory();
        if (await tempDir.exists()) {
          await tempDir.delete(recursive: true);
          LoggerUtils.logInfo('임시 파일 삭제 완료', tag: 'MyPageScreen');
        }
      } catch (e) {
        LoggerUtils.logError('임시 파일 삭제 실패: $e', tag: 'MyPageScreen');
      }

      // 5. Flutter 이미지 캐시 삭제
      try {
        PaintingBinding.instance.imageCache.clear();
        PaintingBinding.instance.imageCache.clearLiveImages();
        LoggerUtils.logInfo('Flutter 이미지 캐시 삭제 완료', tag: 'MyPageScreen');
      } catch (e) {
        LoggerUtils.logError('Flutter 이미지 캐시 삭제 실패: $e', tag: 'MyPageScreen');
      }

      LoggerUtils.logInfo('안전한 로컬 데이터 삭제 완료', tag: 'MyPageScreen');

    } catch (e) {
      LoggerUtils.logError('로컬 데이터 삭제 중 오류: $e', tag: 'MyPageScreen');
      // 오류가 발생해도 계속 진행
    }
  }

  /// Firebase 사용자 설정 삭제 (회원탈퇴 시)
  Future<void> _clearFirebaseUserSettingsForDeletion() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        LoggerUtils.logInfo('사용자가 로그인되어 있지 않음 - Firebase 설정 삭제 건너뜀', tag: 'MyPageScreen');
        return;
      }

      LoggerUtils.logInfo('🗑️ 회원탈퇴: Firebase 사용자 설정 삭제 시작', tag: 'MyPageScreen');

      // 사용자 문서에서 닉네임과 워크스페이스 설정 삭제
      await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .update({
        'nickname': FieldValue.delete(),
        'lastWorkspaceId': FieldValue.delete(),
        'lastWorkspaceUpdatedAt': FieldValue.delete(),
      });

      LoggerUtils.logInfo('✅ 회원탈퇴: Firebase 사용자 설정 삭제 완료', tag: 'MyPageScreen');
    } catch (e) {
      LoggerUtils.logError('회원탈퇴: Firebase 사용자 설정 삭제 실패', tag: 'MyPageScreen', error: e);
      // 실패해도 회원탈퇴는 계속 진행 (서버에서 처리됨)
    }
  }

  /// 회원탈퇴 확인 다이얼로그
  Future<bool> _showDeleteConfirmDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(Icons.warning, color: Colors.red),
        title: const Text('회원탈퇴'),
        content: const Text('정말로 회원탈퇴 하시겠습니까?\n모든 데이터가 완전히 삭제됩니다.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('탈퇴'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  /// 비밀번호 입력 다이얼로그 (안전한 컨트롤러 관리)
  Future<String?> _showPasswordDialog() async {
    return await showDialog<String>(
      context: context,
      builder: (context) => _PasswordDialog(),
    );
  }

  /// 완료 다이얼로그 - 회원탈퇴 완료 후 앱 재시작
  Future<void> _showCompletionDialog() async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('회원탈퇴 완료'),
        content: const Text('회원탈퇴가 완료되었습니다.\n앱이 재시작됩니다.'),
        actions: [
          ElevatedButton(
            onPressed: () async {
              // SharedPreferences에 회원탈퇴 완료 플래그 설정
              final prefs = await SharedPreferences.getInstance();
              await prefs.setBool('force_app_restart', true);

              // Phoenix로 앱 완전 재시작 (모든 상태 초기화)
              Phoenix.rebirth(context);
            },
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }

  /// 프로필 수정 다이얼로그
  void _showProfileEditDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _ProfileEditDialog(
        ref: ref,
        onImageUpload: _pickAndUploadProfileImage,
        loadingImage: _loadingImage,
      ),
    );
  }


  /// 섹션 헤더 위젯 (오렌지색 세로 라인 포함)
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 24, bottom: 8),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 20,
            decoration: BoxDecoration(
              color: AppColors.primarySeed,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    final nickname = ref.watch(nicknameProvider);
    return Scaffold(
      appBar: AppBar(
        title: Builder(builder: (ctx)=> Text('마이페이지', style: AppBarStyles.of(ctx))),
        centerTitle: true,
        backgroundColor: AppColors.primarySeed,
        foregroundColor: Colors.white,
        surfaceTintColor: Colors.transparent, // Material 3에서 색상 덮어쓰기 방지
        elevation: 0,
  // 설정 페이지 진입 버튼 제거됨 (선입금 관련 설정 위치 변경에 따른 간소화)
      ),
      body: SafeArea(
        child: ListView(
          padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
          children: [
            // 프로필 카드
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    // 프로필 이미지
                    Center(
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // 프로필 이미지 표시 (ProfileAvatarWidget 사용으로 통일)
                          if (!_loadingImage)
                            ProfileAvatarWidget(
                              radius: 48,
                              nickname: nickname,
                              backgroundColor: Colors.grey[100],
                            ),
                          // 업로드/크롭 중에는 오버레이+로딩만 단독 노출
                          if (_loadingImage)
                            Container(
                              width: 96,
                              height: 96,
                              decoration: const BoxDecoration(
                                color: Colors.black38,
                                shape: BoxShape.circle,
                              ),
                              child: const Center(child: CircularProgressIndicator()),
                            ),

                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    // 닉네임
                    Text(
                      nickname?.name ?? '닉네임 없음',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 4),
                    // 이메일
                    Text(
                      _userEmail ?? '이메일 로딩 중...',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 16),
                    // 프로필 수정 버튼
                    ElevatedButton(
                      onPressed: () => _showProfileEditDialog(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primarySeed,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                      child: const Text('프로필 수정'),
                    ),
                  ],
                ),
              ),
            ),
            // 구독 & 결제 섹션
            _buildSectionHeader('구독 & 결제'),

            // 🔥 1. 플랜 정보 (제일 위)
            Consumer(
              builder: (context, ref, child) {
                final subscriptionAsync = ref.watch(currentPlanTypeProvider);
                return subscriptionAsync.when(
                  data: (planType) => ListTile(
                    leading: Icon(
                      planType == SubscriptionPlanType.free ? Icons.star_border :
                      planType == SubscriptionPlanType.plus ? Icons.star_half : Icons.star,
                      color: planType == SubscriptionPlanType.free ? Colors.grey :
                             planType == SubscriptionPlanType.plus ? Colors.blue : Colors.amber,
                    ),
                    title: Text(
                      _getPlanName(planType),
                      style: TextStyle(
                        color: planType == SubscriptionPlanType.free ? Colors.grey[700] :
                               planType == SubscriptionPlanType.plus ? Colors.blue[800] : Colors.amber[800],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    subtitle: Text(_getPlanSubtitle(planType)),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () => Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const SubscriptionPlansScreen(),
                      ),
                    ),
                  ),
                  loading: () => const ListTile(
                    leading: CircularProgressIndicator(),
                    title: Text('플랜 정보 로딩 중...'),
                  ),
                  error: (_, __) => const ListTile(
                    leading: Icon(Icons.error, color: Colors.red),
                    title: Text('플랜 정보 오류'),
                  ),
                );
              },
            ),

            // 🔥 2. 구독 관리
            ListTile(
              leading: const Icon(Icons.credit_card, color: Colors.blue),
              title: const Text('구독 관리'),
              subtitle: const Text('플랜 변경 • 결제 내역 • 카드 관리'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _navigateToSubscriptionManagement,
            ),

            // 계정 설정 섹션
            _buildSectionHeader('계정 설정'),

            // 실시간 동기화는 이제 행사별로 관리됩니다 (행사 관리 페이지에서 설정)

            // 계정 관리
            ListTile(
              leading: const Icon(Icons.person, color: Colors.orange),
              title: const Text('계정 관리'),
              subtitle: const Text('비밀번호 변경, 로그아웃, 회원탈퇴'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _showAccountManagement,
            ),

            // 자주 묻는 질문 (제일 하단으로 이동)
            ListTile(
              leading: const Icon(Icons.help_outline, color: Colors.green),
              title: const Text('자주 묻는 질문'),
              subtitle: const Text('상품관리, 판매진행, 결제/환불, 문제해결'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: _navigateToFAQ,
            ),
          ],
        ),
      ),
    );
  }

  /// FAQ 페이지로 이동
  void _navigateToFAQ() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const FAQScreen(),
      ),
    );
  }
}

/// 안전한 비밀번호 입력 다이얼로그
class _PasswordDialog extends StatefulWidget {
  @override
  _PasswordDialogState createState() => _PasswordDialogState();
}

class _PasswordDialogState extends State<_PasswordDialog> {
  late final TextEditingController _passwordController;

  @override
  void initState() {
    super.initState();
    _passwordController = TextEditingController();
  }

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('비밀번호 확인'),
      content: TextField(
        controller: _passwordController,
        obscureText: true,
        autofocus: true,
        decoration: const InputDecoration(
          labelText: '현재 비밀번호',
          border: OutlineInputBorder(),
        ),
        onSubmitted: (value) {
          if (value.isNotEmpty) {
            Navigator.of(context).pop(value);
          }
        },
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('취소'),
        ),
        ElevatedButton(
          onPressed: () {
            final password = _passwordController.text;
            if (password.isNotEmpty) {
              Navigator.of(context).pop(password);
            }
          },
          child: const Text('확인'),
        ),
      ],
    );
  }
}

/// 프로필 수정 다이얼로그 (StatefulWidget으로 분리)
class _ProfileEditDialog extends StatefulWidget {
  final WidgetRef ref;
  final Future<void> Function() onImageUpload;
  final bool loadingImage;

  const _ProfileEditDialog({
    required this.ref,
    required this.onImageUpload,
    required this.loadingImage,
  });

  @override
  State<_ProfileEditDialog> createState() => _ProfileEditDialogState();
}

class _ProfileEditDialogState extends State<_ProfileEditDialog> {
  late TextEditingController _nicknameController;
  late FocusNode _focusNode;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    final nickname = widget.ref.read(nicknameProvider);
    _nicknameController = TextEditingController(text: nickname?.name ?? '');
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _focusNode.unfocus(); // Focus 명시적 해제
    _focusNode.dispose();
    _nicknameController.dispose();
    super.dispose();
  }

  void _closeDialog() {
    _focusNode.unfocus(); // Focus 해제
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final nickname = widget.ref.watch(nicknameProvider);

    return AlertDialog(
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text('프로필 수정'),
          IconButton(
            onPressed: _isSaving ? null : _closeDialog,
            icon: const Icon(Icons.close),
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 프로필 이미지 섹션
            Center(
              child: Stack(
                alignment: Alignment.center,
                children: [
                  if (!widget.loadingImage)
                    ProfileAvatarWidget(
                      radius: 40,
                      nickname: nickname,
                      backgroundColor: Colors.grey[100],
                    ),
                  if (widget.loadingImage)
                    Container(
                      width: 80,
                      height: 80,
                      decoration: const BoxDecoration(
                        color: Colors.black38,
                        shape: BoxShape.circle,
                      ),
                      child: const Center(child: CircularProgressIndicator()),
                    ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: IconButton(
                      icon: const Icon(Icons.camera_alt, color: Colors.blue),
                      onPressed: _isSaving ? null : () async {
                        _closeDialog(); // 안전하게 닫기
                        await widget.onImageUpload();

                        // 이미지 업데이트 완료 후 약간의 지연을 두고 다이얼로그 다시 열기
                        await Future.delayed(const Duration(milliseconds: 200));

                        // 다이얼로그 다시 열기
                        if (context.mounted) {
                          showDialog(
                            context: context,
                            barrierDismissible: false,
                            builder: (context) => _ProfileEditDialog(
                              ref: widget.ref,
                              onImageUpload: widget.onImageUpload,
                              loadingImage: widget.loadingImage,
                            ),
                          );
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            // 닉네임 입력 필드
            TextField(
              controller: _nicknameController,
              focusNode: _focusNode,
              decoration: const InputDecoration(
                labelText: '닉네임',
                hintText: '새 닉네임을 입력하세요',
                border: OutlineInputBorder(),
              ),
              autofocus: false,
            ),
          ],
        ),
      ),
      actions: [
        ElevatedButton(
          onPressed: _isSaving ? null : () async {
            final newNickname = _nicknameController.text.trim();

            if (newNickname.isNotEmpty && newNickname != nickname?.name) {
              setState(() {
                _isSaving = true;
              });

              try {
                await widget.ref.read(nicknameProvider.notifier).setNickname(newNickname);
                if (mounted && context.mounted) {
                  ToastUtils.showSuccess(context, '닉네임이 변경되었습니다.');
                }
              } catch (e) {
                if (mounted && context.mounted) {
                  ToastUtils.showError(context, '닉네임 변경에 실패했습니다: $e');
                }
              } finally {
                if (mounted) {
                  setState(() {
                    _isSaving = false;
                  });
                }
              }
            } else {
              ToastUtils.showInfo(context, '변경할 내용이 없습니다.');
            }
          },
          child: _isSaving
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text('저장'),
        ),
      ],
    );
  }
}
