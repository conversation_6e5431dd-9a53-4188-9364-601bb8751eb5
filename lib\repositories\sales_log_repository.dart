import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sqflite/sqflite.dart';
import '../models/sales_log.dart';
import '../models/sales_stat_item.dart';
import '../models/transaction_type.dart';
import '../services/database_service.dart';
import '../utils/cancellation_token.dart';
import '../utils/logger_utils.dart';
import '../utils/network_status.dart';
import '../utils/offline_task.dart';
import '../utils/sql_utils.dart';
import 'sales_log_crud.dart';


final salesLogRepositoryProvider = Provider<SalesLogRepository>((ref) {
  throw UnimplementedError('SalesLogRepository must be initialized before use');
});

/// 판매 기록(영수증/로그) 데이터의 CRUD, 검색, 통계, 보안 등 관리 기능을 제공하는 Repository 클래스입니다.
/// - 로컬 DB(SQLite)와 연동하며, 오프라인 작업 큐/동기화 구조를 지원합니다.
/// - SQL injection 방지, 날짜/정렬/검색 등 다양한 필터링 지원
class SalesLogRepository {
  final DatabaseService _databaseService;
  late final SalesLogCrud _crud;
  
  // 테이블명 및 허용된 테이블 목록
  static const String salesLogTable = 'sales_log'; // 일관된 테이블명 사용
  static const List<String> _allowedTables = [salesLogTable];

  SalesLogRepository({required DatabaseService database})
    : _databaseService = database {
    _crud = SalesLogCrud(database: database);
  }

  Future<Database> get _database => _databaseService.database;

  // 모든 거래 기록 조회 (최신순)
  Future<List<SalesLog>> getAllSalesLogs() async {
    return await _crud.getAllSalesLogs();
  }

  // 특정 행사의 모든 거래 기록 조회 (최신순)
  Future<List<SalesLog>> getSalesLogsByEventId(int eventId) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseServiceImpl.salesLogTable,
      where: 'eventId = ?',
      whereArgs: [eventId],
      orderBy: 'saleTimestamp DESC',
    );
    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // 특정 타입의 모든 거래 기록 조회 (최신순)
  Future<List<SalesLog>> getAllSalesLogsByType(
    TransactionType transactionType,
  ) async {
    return await _crud.getAllSalesLogsByType(transactionType);
  }

  // 판매자별 모든 거래 기록 조회 (최신순)
  Future<List<SalesLog>> getSalesLogsBySeller(String sellerName) async {
    return await _crud.getSalesLogsBySeller(sellerName);
  }

  // 판매자별 특정 타입의 거래 기록 조회 (최신순)
  Future<List<SalesLog>> getSalesLogsBySellerAndType(
    String sellerName,
    TransactionType transactionType,
  ) async {
    return await _crud.getSalesLogsBySellerAndType(sellerName, transactionType);
  }

  // 모든 고유한 판매자 이름 목록 조회
  Future<List<String>> getAllDistinctSellerNames() async {
    return await _crud.getAllDistinctSellerNames();
  }

  // 전체 판매 통계 (행사별 필터링 지원)
  Future<List<SalesStatItem>> getOverallSalesStats({int? eventId}) async {
    if (!SqlUtils.isValidTableName(salesLogTable, _allowedTables)) {
      throw ArgumentError('Invalid table name');
    }

    final db = await _database;
    final whereClause = eventId != null
      ? 'WHERE sl.sellerName IS NOT NULL AND sl.eventId = ?'
      : 'WHERE sl.sellerName IS NOT NULL';

    final queryParams = eventId != null
      ? [
          TransactionType.sale.value,
          TransactionType.discount.value,
          TransactionType.sale.value,
          TransactionType.discount.value,
          TransactionType.sale.value,
          eventId,
        ]
      : [
          TransactionType.sale.value,
          TransactionType.discount.value,
          TransactionType.sale.value,
          TransactionType.discount.value,
          TransactionType.sale.value,
        ];

    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT
        sl.sellerName,
        SUM(CASE WHEN sl.transactionType = ? THEN sl.totalAmount ELSE 0 END) as totalSalesAmount,
        SUM(CASE WHEN sl.transactionType = ? THEN sl.totalAmount ELSE 0 END) as totalDiscountAmount,
        (SUM(CASE WHEN sl.transactionType = ? THEN sl.totalAmount ELSE 0 END) -
         SUM(CASE WHEN sl.transactionType = ? THEN sl.totalAmount ELSE 0 END)) as netSalesAmount,
        SUM(CASE WHEN sl.transactionType = ? THEN 1 ELSE 0 END) as salesCount
      FROM $salesLogTable sl
      $whereClause
      GROUP BY sl.sellerName
      ORDER BY sl.sellerName ASC
    ''',
      queryParams,
    );

    return maps.map((map) => SalesStatItem.fromMap(map)).toList();
  }

  // 특정 판매자 판매 통계
  Future<List<SalesStatItem>> getSalesStatsBySeller(String sellerName) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT
        sl.sellerName,
        SUM(CASE WHEN sl.transactionType = 'SALE' THEN sl.totalAmount ELSE 0 END) as totalSalesAmount,
        SUM(CASE WHEN sl.transactionType = 'DISCOUNT' THEN sl.totalAmount ELSE 0 END) as totalDiscountAmount,
        (SUM(CASE WHEN sl.transactionType = 'SALE' THEN sl.totalAmount ELSE 0 END) - 
         SUM(CASE WHEN sl.transactionType = 'DISCOUNT' THEN sl.totalAmount ELSE 0 END)) as netSalesAmount,
        SUM(CASE WHEN sl.transactionType = 'SALE' THEN 1 ELSE 0 END) as salesCount
      FROM $salesLogTable sl
      WHERE sl.sellerName = ?
      GROUP BY sl.sellerName
      ORDER BY sl.sellerName ASC
    ''',
      [sellerName],
    );

    return maps.map((map) => SalesStatItem.fromMap(map)).toList();
  }

  // 기간 및 판매자별 "판매" 총액 합계
  Future<int> getTotalSalesAmount(
    int startTime,
    int endTime,
    String? sellerName,
  ) async {
    final db = await _database;
    String query =
        '''
      SELECT SUM(totalAmount - setDiscountAmount - manualDiscountAmount) as total
      FROM $salesLogTable
      WHERE transactionType = ?
      AND saleTimestamp BETWEEN ? AND ?
    ''';
    List<dynamic> args = [TransactionType.sale.value, startTime, endTime];

    if (sellerName != null) {
      query += ' AND sellerName = ?';
      args.add(sellerName);
    }

    final result = await db.rawQuery(query, args);
    return (result.first['total'] as int?) ?? 0;
  }

  // 모든 기간, 모든 판매자 "판매" 총액 합계 (행사별 필터링 지원)
  Future<int> getTotalSalesAmountAll({int? eventId}) async {
    final db = await _database;

    final whereClause = eventId != null
      ? 'WHERE transactionType = ? AND eventId = ?'
      : 'WHERE transactionType = ?';

    final queryParams = eventId != null
      ? [TransactionType.sale.value, eventId]
      : [TransactionType.sale.value];

    final result = await db.rawQuery(
      '''
      SELECT SUM(totalAmount - setDiscountAmount - manualDiscountAmount) as total
      FROM $salesLogTable
      $whereClause
    ''',
      queryParams,
    );

    return (result.first['total'] as int?) ?? 0;
  }

  // 특정 판매자의 모든 기간 "판매" 총액 합계
  Future<int> getTotalSalesAmountBySeller(String sellerName) async {
    final db = await _database;
    final result = await db.rawQuery(
      '''
      SELECT SUM(totalAmount - setDiscountAmount - manualDiscountAmount) as total
      FROM $salesLogTable
      WHERE sellerName = ? AND transactionType = ?
    ''',
      [sellerName, TransactionType.sale.value],
    );

    return (result.first['total'] as int?) ?? 0;
  }

  // 기간 및 판매자별 "할인" 총액 합계
  Future<int> getTotalDiscountAmount(
    int startTime,
    int endTime,
    String? sellerName,
  ) async {
    final db = await _database;
    String query =
        '''
      SELECT SUM(totalAmount) as total 
      FROM $salesLogTable 
      WHERE transactionType = ? 
      AND saleTimestamp BETWEEN ? AND ?
    ''';
    List<dynamic> args = [TransactionType.discount.value, startTime, endTime];

    if (sellerName != null) {
      query += ' AND sellerName = ?';
      args.add(sellerName);
    }

    final result = await db.rawQuery(query, args);
    return (result.first['total'] as int?) ?? 0;
  }

  // 모든 기간, 모든 판매자 "할인" 총액 합계
  Future<int> getTotalDiscountAmountAll() async {
    final db = await _database;
    final result = await db.rawQuery(
      '''
      SELECT SUM(totalAmount) as total 
      FROM $salesLogTable 
      WHERE transactionType = ?
    ''',
      [TransactionType.discount.value],
    );

    return (result.first['total'] as int?) ?? 0;
  }

  // 특정 판매자의 모든 기간 "할인" 총액 합계
  Future<int> getTotalDiscountAmountBySeller(String sellerName) async {
    final db = await _database;
    final result = await db.rawQuery(
      '''
      SELECT SUM(totalAmount) as total 
      FROM $salesLogTable 
      WHERE sellerName = ? AND transactionType = ?
    ''',
      [sellerName, TransactionType.discount.value],
    );

    return (result.first['total'] as int?) ?? 0;
  }

  // 날짜 범위별 판매 기록 조회
  Future<List<SalesLog>> getSalesLogsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _database;
    final startTimestamp = startDate.millisecondsSinceEpoch;
    final endTimestamp = endDate.millisecondsSinceEpoch;

    final List<Map<String, dynamic>> maps = await db.query(
      salesLogTable,
      where: 'saleTimestamp BETWEEN ? AND ?',
      whereArgs: [startTimestamp, endTimestamp],
      orderBy: 'saleTimestamp DESC',
    );

    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // 배치 ID별 판매 기록 조회
  Future<List<SalesLog>> getSalesLogsByBatchId(String batchId) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      salesLogTable,
      where: 'batchSaleId = ?',
      whereArgs: [batchId],
      orderBy: 'saleTimestamp DESC',
    );

    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // ID로 판매 기록 조회
  Future<SalesLog?> getLogById(int id) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      salesLogTable,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return SalesLog.fromMap(maps.first);
    }
    return null;
  }

  // 내부 구현용 판매 기록 삽입 (private)
  Future<int> _insertLog(SalesLog log) async {
    if (!NetworkStatusUtil.isOnline) {
      final task = OfflineTask(
        id: DateTime.now().microsecondsSinceEpoch.toString(),
        type: OfflineTaskType.insert,
        table: salesLogTable,
        data: log.toMap(),
        timestamp: DateTime.now(),
      );
      await (_databaseService as DatabaseServiceImpl).addOfflineTask(
        task.toMap(),
      );
      // 오프라인 상태에서는 임시 ID 생성 (음수로 구분)
      return -DateTime.now().microsecondsSinceEpoch;
    }
    final db = await _database;
    
    // 새로운 레코드 삽입 시 ID 필드 제외
    final data = log.toMap();
    data.remove('id'); // ID 필드 제거하여 자동 증가 ID 사용
    
    return await db.insert(
      salesLogTable,
      data,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  // 내부 구현용 판매 기록 업데이트 (private)
  Future<int> _updateLog(SalesLog log) async {
    if (!NetworkStatusUtil.isOnline) {
      final task = OfflineTask(
        id: DateTime.now().microsecondsSinceEpoch.toString(),
        type: OfflineTaskType.update,
        table: salesLogTable,
        data: log.toMap(),
        timestamp: DateTime.now(),
      );
      await (_databaseService as DatabaseServiceImpl).addOfflineTask(
        task.toMap(),
      );
      return -1;
    }
    final db = await _database;
    return await db.update(
      salesLogTable,
      log.toMap(),
      where: 'id = ?',
      whereArgs: [log.id],
    );
  }

  // 모든 판매 기록 삭제
  Future<int> deleteAllLogs() async {
    final db = await _database;
    return await db.delete(salesLogTable);
  }

  // 판매자별 판매 기록 삭제
  Future<int> deleteLogsBySeller(String sellerName) async {
    final db = await _database;
    return await db.delete(
      salesLogTable,
      where: 'sellerName = ?',
      whereArgs: [sellerName],
    );
  }

  // 날짜 범위별 판매 기록 삭제
  Future<int> deleteLogsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await _database;
    final startTimestamp = startDate.millisecondsSinceEpoch;
    final endTimestamp = endDate.millisecondsSinceEpoch;

    return await db.delete(
      salesLogTable,
      where: 'saleTimestamp BETWEEN ? AND ?',
      whereArgs: [startTimestamp, endTimestamp],
    );
  }

  // 배치 ID별 판매 기록 삭제
  Future<int> deleteLogsByBatchId(String batchId) async {
    final db = await _database;
    return await db.delete(
      salesLogTable,
      where: 'batchSaleId = ?',
      whereArgs: [batchId],
    );
  }

  // 판매 기록 추가
  Future<SalesLog> addSalesLog(SalesLog salesLog) async {
    final id = await _insertLog(salesLog);

    // ID가 유효한지 확인 (오프라인 상태에서는 음수 ID가 정상)
    if (id == 0) {
      throw Exception('판매 기록 저장 실패: 유효하지 않은 ID ($id)');
    }

    final salesLogWithId = salesLog.copyWith(id: id);

    // 실시간 동기화는 SalesLogNotifier에서 처리하므로 여기서는 하지 않음 (중복 방지)
    // Firebase 업로드는 SalesLogNotifier에서 dataSyncService.uploadSingleSalesLog()로 처리됨

    return salesLogWithId;
  }

  // 판매 기록 업데이트
  Future<int> updateSalesLog(SalesLog salesLog) async {
    final result = await _updateLog(salesLog);

    // 실시간 동기화는 SalesLogNotifier에서 처리하므로 여기서는 하지 않음 (중복 방지)
    // Firebase 업로드는 SalesLogNotifier에서 dataSyncService.uploadSingleSalesLog()로 처리됨

    return result;
  }

  /// [salesLogId]: 삭제할 SalesLog의 ID
  /// 반환값: 삭제된 row 개수(오프라인 큐 저장 시에도 row 개수 반환)
  /// 예외: DB 오류/네트워크 상태 등
  Future<int> deleteSalesLog(int salesLogId) async {
    final db = await _database;
    // 로컬 DB에서 삭제
    final deletedRows = await db.delete(
      salesLogTable,
      where: 'id = ?',
      whereArgs: [salesLogId],
    );

    // 실시간 동기화는 SalesLogNotifier에서 처리하므로 여기서는 하지 않음 (중복 방지)
    // Firebase 삭제는 SalesLogNotifier에서 처리됨

    // 4. 오프라인일 때는 동기화 큐에도 기록
    if (!NetworkStatusUtil.isOnline) {
      final task = OfflineTask(
        id: DateTime.now().microsecondsSinceEpoch.toString(),
        type: OfflineTaskType.delete,
        table: salesLogTable,
        data: {'id': salesLogId},
        timestamp: DateTime.now(),
      );
      await (_databaseService as DatabaseServiceImpl).addOfflineTask(
        task.toMap(),
      );
    }
    LoggerUtils.logDebug('[삭제] 판매기록 id=$salesLogId, deletedRows=$deletedRows', tag: 'SalesLogRepository');
    return deletedRows;
  }

  // ========== 페이지네이션 지원 메서드들 ==========

  /// 페이지네이션을 지원하는 전체 판매 기록 조회
  Future<List<SalesLog>> getSalesLogsPaginated(
    int page,
    int pageSize,
    String? searchQuery,
    String? sortOrder,
  ) async {
    final db = await _database;

    // WHERE 절 구성
    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereClause =
          'WHERE (sellerName LIKE ? OR productName LIKE ? OR batchSaleId LIKE ?)';
      final searchPattern = '%${SqlUtils.escapeLikePattern(searchQuery)}%';
      whereArgs.addAll([searchPattern, searchPattern, searchPattern]);
    }

    // ORDER BY 절 구성
    String orderBy = 'saleTimestamp DESC'; // 기본: 최신순
    if (sortOrder != null && sortOrder.isNotEmpty) {
      final validColumns = [
        'saleTimestamp',
        'sellerName',
        'productName',
        'totalAmount',
      ];
      if (validColumns.contains(sortOrder.split(' ')[0])) {
        orderBy = '${sortOrder.split(' ')[0]} ${sortOrder.split(' ')[1]}';
      }
    }

    // 페이지네이션 쿼리 실행
    final query =
        '''
      SELECT * FROM $salesLogTable
      $whereClause
      ORDER BY $orderBy
      LIMIT ? OFFSET ?
    ''';

    final queryArgs = [pageSize, page * pageSize];
    final List<Map<String, dynamic>> maps = await db.rawQuery(query, queryArgs);

    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  /// 판매자별 페이지네이션 판매 기록 조회
  Future<List<SalesLog>> getSalesLogsBySellerPaginated(
    String sellerName,
    int page,
    int pageSize,
    String? searchQuery,
    String? sortOrder,
  ) async {
    final db = await _database;

    // WHERE 절 구성
    String whereClause = 'WHERE sellerName = ?';
    List<dynamic> whereArgs = [sellerName];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereClause += ' AND (productName LIKE ? OR batchSaleId LIKE ?)';
      final searchPattern = '%${SqlUtils.escapeLikePattern(searchQuery)}%';
      whereArgs.addAll([searchPattern, searchPattern]);
    }

    // ORDER BY 절 구성
    String orderBy = 'saleTimestamp DESC';
    if (sortOrder != null && sortOrder.isNotEmpty) {
      final validColumns = ['saleTimestamp', 'productName', 'totalAmount'];
      if (validColumns.contains(sortOrder.split(' ')[0])) {
        orderBy = '${sortOrder.split(' ')[0]} ${sortOrder.split(' ')[1]}';
      }
    }

    // 페이지네이션 쿼리 실행
    final query =
        '''
      SELECT * FROM $salesLogTable
      $whereClause
      ORDER BY $orderBy
      LIMIT ? OFFSET ?
    ''';

    final queryArgs = [pageSize, page * pageSize];
    final List<Map<String, dynamic>> maps = await db.rawQuery(query, queryArgs);

    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  /// 거래 타입별 페이지네이션 판매 기록 조회
  Future<List<SalesLog>> getSalesLogsByTypePaginated(
    TransactionType transactionType,
    int page,
    int pageSize,
    String? searchQuery,
    String? sortOrder,
  ) async {
    final db = await _database;

    // WHERE 절 구성
    String whereClause = 'WHERE transactionType = ?';
    List<dynamic> whereArgs = [transactionType.value];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereClause +=
          ' AND (sellerName LIKE ? OR productName LIKE ? OR batchSaleId LIKE ?)';
      final searchPattern = '%${SqlUtils.escapeLikePattern(searchQuery)}%';
      whereArgs.addAll([searchPattern, searchPattern, searchPattern]);
    }

    // ORDER BY 절 구성
    String orderBy = 'saleTimestamp DESC';
    if (sortOrder != null && sortOrder.isNotEmpty) {
      final validColumns = [
        'saleTimestamp',
        'sellerName',
        'productName',
        'totalAmount',
      ];
      if (validColumns.contains(sortOrder.split(' ')[0])) {
        orderBy = '${sortOrder.split(' ')[0]} ${sortOrder.split(' ')[1]}';
      }
    }

    // 페이지네이션 쿼리 실행
    final query =
        '''
      SELECT * FROM $salesLogTable
      $whereClause
      ORDER BY $orderBy
      LIMIT ? OFFSET ?
    ''';

    final queryArgs = [pageSize, page * pageSize];
    final List<Map<String, dynamic>> maps = await db.rawQuery(query, queryArgs);

    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  /// 날짜 범위별 페이지네이션 판매 기록 조회
  Future<List<SalesLog>> getSalesLogsByDateRangePaginated(
    DateTime startDate,
    DateTime endDate,
    int page,
    int pageSize,
    String? searchQuery,
    String? sortOrder,
  ) async {
    final db = await _database;
    final startTimestamp = startDate.millisecondsSinceEpoch;
    final endTimestamp = endDate.millisecondsSinceEpoch;

    // WHERE 절 구성
    String whereClause = 'WHERE saleTimestamp BETWEEN ? AND ?';
    List<dynamic> whereArgs = [startTimestamp, endTimestamp];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereClause +=
          ' AND (sellerName LIKE ? OR productName LIKE ? OR batchSaleId LIKE ?)';
      final searchPattern = '%${SqlUtils.escapeLikePattern(searchQuery)}%';
      whereArgs.addAll([searchPattern, searchPattern, searchPattern]);
    }

    // ORDER BY 절 구성
    String orderBy = 'saleTimestamp DESC';
    if (sortOrder != null && sortOrder.isNotEmpty) {
      final validColumns = [
        'saleTimestamp',
        'sellerName',
        'productName',
        'totalAmount',
      ];
      if (validColumns.contains(sortOrder.split(' ')[0])) {
        orderBy = '${sortOrder.split(' ')[0]} ${sortOrder.split(' ')[1]}';
      }
    }

    // 페이지네이션 쿼리 실행
    final query =
        '''
      SELECT * FROM $salesLogTable
      $whereClause
      ORDER BY $orderBy
      LIMIT ? OFFSET ?
    ''';

    final queryArgs = [pageSize, page * pageSize];
    final List<Map<String, dynamic>> maps = await db.rawQuery(query, queryArgs);

    return maps.map((map) => SalesLog.fromMap(map)).toList();
  }

  // 대용량 거래 기록 배치 처리
  Future<void> processSalesLogsBatch({
    required List<SalesLog> salesLogs,
    required Future<void> Function(List<SalesLog> batch) processor,
    void Function(double progress)? onProgress,
    void Function(SalesLog item, dynamic error)? onItemError,
    int batchSize = 100,
    Duration? timeout,
    Duration? retryDelay,
    int? maxRetries,
    CancellationToken? cancellationToken,
  }) async {
    // 배치 단위로 처리
    final totalItems = salesLogs.length;
    var processedCount = 0;

    for (int i = 0; i < totalItems; i += batchSize) {
      final endIndex = (i + batchSize < totalItems) ? i + batchSize : totalItems;
      final batch = salesLogs.sublist(i, endIndex);

      try {
        await processor(batch);
        processedCount += batch.length;

        // 진행률 업데이트
        final progress = processedCount / totalItems;
        onProgress?.call(progress);

        LoggerUtils.logDebug(
          'Batch processed: $processedCount/$totalItems (${(progress * 100).toStringAsFixed(1)}%)',
          tag: 'SalesLogRepository',
        );
      } catch (e) {
        LoggerUtils.logError(
          'Batch processing error: $e',
          error: e,
          tag: 'SalesLogRepository',
        );

        // 개별 아이템 에러 처리
        for (final item in batch) {
          onItemError?.call(item, e);
        }

        // 재시도 로직
        if (maxRetries != null && maxRetries > 0) {
          await Future.delayed(retryDelay ?? const Duration(seconds: 1));
          i -= batchSize; // 현재 배치 재시도
          maxRetries--;
        }
      }

      // 취소 토큰 확인
      if (cancellationToken?.isCancelled == true) {
        LoggerUtils.logInfo(
          'Batch processing cancelled',
          tag: 'SalesLogRepository',
        );
        break;
      }
    }
  }

  // 대용량 거래 기록 일괄 삽입
  Future<void> insertSalesLogsBatch(
    List<SalesLog> salesLogs, {
    void Function(double progress)? onProgress,
    void Function(SalesLog item, dynamic error)? onItemError,
    CancellationToken? cancellationToken,
  }) async {
    final db = await _database;

    await processSalesLogsBatch(
      salesLogs: salesLogs,
      processor: (batchItems) async {  // 변수명 변경으로 충돌 방지
        await db.transaction((txn) async {
          final dbBatch = txn.batch();  // 변수명 변경으로 충돌 방지

          for (final log in batchItems) {  // 올바른 배치 아이템 사용
            dbBatch.insert(
              salesLogTable,
              log.toMap(),
              conflictAlgorithm: ConflictAlgorithm.replace,
            );
          }

          await dbBatch.commit(noResult: true);
        });
      },
      onProgress: onProgress,
      onItemError: onItemError,
      cancellationToken: cancellationToken,
    );
  }

  // 대용량 거래 기록 일괄 업데이트
  Future<void> updateSalesLogsBatch(
    List<SalesLog> salesLogs, {
    void Function(double progress)? onProgress,
    void Function(SalesLog item, dynamic error)? onItemError,
    CancellationToken? cancellationToken,
  }) async {
    final db = await _database;

    await processSalesLogsBatch(
      salesLogs: salesLogs,
      processor: (batch) async {
        await db.transaction((txn) async {
          final batch = txn.batch();

          for (final log in salesLogs) {
            batch.update(
              salesLogTable,
              log.toMap(),
              where: 'id = ?',
              whereArgs: [log.id],
              conflictAlgorithm: ConflictAlgorithm.replace,
            );
          }

          await batch.commit(noResult: true);
        });
      },
      onProgress: onProgress,
      onItemError: onItemError,
      cancellationToken: cancellationToken,
    );
  }

  // 대용량 거래 기록 일괄 삭제
  Future<void> deleteSalesLogsBatch(
    List<SalesLog> salesLogs, {
    void Function(double progress)? onProgress,
    void Function(SalesLog item, dynamic error)? onItemError,
    CancellationToken? cancellationToken,
  }) async {
    final db = await _database;

    await processSalesLogsBatch(
      salesLogs: salesLogs,
      processor: (batch) async {
        await db.transaction((txn) async {
          final batch = txn.batch();

          for (final log in salesLogs) {
            batch.delete(
              salesLogTable,
              where: 'id = ?',
              whereArgs: [log.id],
            );
          }

          await batch.commit(noResult: true);
        });
      },
      onProgress: onProgress,
      onItemError: onItemError,
      cancellationToken: cancellationToken,
    );
  }

  /// 판매 기록 조회 (페이지네이션 지원)
  Future<List<SalesLog>> getSalesLogs({
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? sortOrder,
    int? limit,
    int? offset,
  }) async {
    final db = await _database;

    var query = '''
      SELECT * FROM $salesLogTable
      WHERE 1=1
    ''';

    final args = <Object>[];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      query += ' AND (description LIKE ? OR seller_name LIKE ?)';
      final pattern = '%${SqlUtils.escapeLikePattern(searchQuery)}%';
      args.addAll([pattern, pattern]);
    }

    if (startDate != null) {
      query += ' AND date >= ?';
      args.add(startDate.toIso8601String());
    }

    if (endDate != null) {
      query += ' AND date <= ?';
      args.add(endDate.toIso8601String());
    }

    if (sortOrder != null && sortOrder.isNotEmpty) {
      final validatedOrder = SqlUtils.validateOrderBy(sortOrder, [
        'date',
        'amount',
        'seller_name',
        'description',
      ]);
      query += ' ORDER BY $validatedOrder';
    } else {
      query += ' ORDER BY date DESC';
    }

    // 페이지네이션 적용
    if (limit != null) {
      query += ' LIMIT $limit';
      if (offset != null) {
        query += ' OFFSET $offset';
      }
    }

    final results = await db.rawQuery(query, args);
    return results.map((row) => SalesLog.fromMap(row)).toList();
  }

  /// 판매 기록 페이지네이션 조회 (POS 최적화)
  Future<List<SalesLog>> getSalesLogsByEventPaginated({
    required int eventId,
    int page = 0,
    int pageSize = 50,
    String? searchQuery,
    String? sellerFilter,
  }) async {
    final db = await _database;

    var query = '''
      SELECT * FROM $salesLogTable
      WHERE eventId = ?
    ''';

    final args = <Object>[eventId];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      query += ' AND (productName LIKE ? OR sellerName LIKE ? OR memo LIKE ?)';
      final pattern = '%${SqlUtils.escapeLikePattern(searchQuery)}%';
      args.addAll([pattern, pattern, pattern]);
    }

    if (sellerFilter != null && sellerFilter.isNotEmpty) {
      query += ' AND sellerName = ?';
      args.add(sellerFilter);
    }

    query += ' ORDER BY saleTimestamp DESC';
    query += ' LIMIT $pageSize OFFSET ${page * pageSize}';

    final results = await db.rawQuery(query, args);
    return results.map((row) => SalesLog.fromMap(row)).toList();
  }

  /// 판매 기록 총 개수 조회 (페이지네이션용)
  Future<int> getSalesLogsCount({
    required int eventId,
    String? searchQuery,
    String? sellerFilter,
  }) async {
    final db = await _database;

    var query = '''
      SELECT COUNT(*) as count FROM $salesLogTable
      WHERE eventId = ?
    ''';

    final args = <Object>[eventId];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      query += ' AND (productName LIKE ? OR sellerName LIKE ? OR memo LIKE ?)';
      final pattern = '%${SqlUtils.escapeLikePattern(searchQuery)}%';
      args.addAll([pattern, pattern, pattern]);
    }

    if (sellerFilter != null && sellerFilter.isNotEmpty) {
      query += ' AND sellerName = ?';
      args.add(sellerFilter);
    }

    final results = await db.rawQuery(query, args);
    return results.first['count'] as int;
  }

  /// 판매 기록 일괄 처리
  Future<void> processBatch({
    required List<String> logIds,
    required String operation,
    void Function(double progress)? onProgress,
  }) async {
    final db = await _database;

    await db.transaction((txn) async {
      final total = logIds.length;
      var processed = 0;

      for (final id in logIds) {
        switch (operation) {
          case 'delete':
            await txn.delete(salesLogTable, where: 'id = ?', whereArgs: [id]);
            break;

          case 'archive':
            await txn.update(
              salesLogTable,
              {'is_archived': 1},
              where: 'id = ?',
              whereArgs: [id],
            );
            break;

          default:
            throw ArgumentError('지원하지 않는 작업입니다: $operation');
        }

        processed++;
        onProgress?.call(processed / total);
      }
    });
  }

  /// 기간별 판매 통계 조회
  Future<List<SalesStatItem>> getSalesStatsByPeriod({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final db = await _database;
    final startTimestamp = startDate.millisecondsSinceEpoch;
    final endTimestamp = endDate.millisecondsSinceEpoch;

    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT
        sl.sellerName,
        SUM(CASE WHEN sl.transactionType = ? THEN sl.totalAmount ELSE 0 END) as totalSalesAmount,
        SUM(CASE WHEN sl.transactionType = ? THEN sl.totalAmount ELSE 0 END) as totalDiscountAmount,
        (SUM(CASE WHEN sl.transactionType = ? THEN sl.totalAmount ELSE 0 END) - 
         SUM(CASE WHEN sl.transactionType = ? THEN sl.totalAmount ELSE 0 END)) as netSalesAmount,
        SUM(CASE WHEN sl.transactionType = ? THEN 1 ELSE 0 END) as salesCount
      FROM $salesLogTable sl
      WHERE sl.saleTimestamp BETWEEN ? AND ?
      GROUP BY sl.sellerName
      ORDER BY sl.sellerName ASC
    ''',
      [
        TransactionType.sale.value,
        TransactionType.discount.value,
        TransactionType.sale.value,
        TransactionType.discount.value,
        TransactionType.sale.value,
        startTimestamp,
        endTimestamp,
      ],
    );

    return maps.map((map) => SalesStatItem.fromMap(map)).toList();
  }

  /// 상품별 판매 통계 조회
  Future<List<SalesStatItem>> getSalesStatsByProduct(String productName) async {
    final db = await _database;

    final List<Map<String, dynamic>> maps = await db.rawQuery(
      '''
      SELECT
        sl.sellerName,
        SUM(CASE WHEN sl.transactionType = ? THEN sl.totalAmount ELSE 0 END) as totalSalesAmount,
        SUM(CASE WHEN sl.transactionType = ? THEN sl.totalAmount ELSE 0 END) as totalDiscountAmount,
        (SUM(CASE WHEN sl.transactionType = ? THEN sl.totalAmount ELSE 0 END) - 
         SUM(CASE WHEN sl.transactionType = ? THEN sl.totalAmount ELSE 0 END)) as netSalesAmount,
        SUM(CASE WHEN sl.transactionType = ? THEN 1 ELSE 0 END) as salesCount
      FROM $salesLogTable sl
      WHERE sl.productName LIKE ?
      GROUP BY sl.sellerName
      ORDER BY sl.sellerName ASC
    ''',
      [
        TransactionType.sale.value,
        TransactionType.discount.value,
        TransactionType.sale.value,
        TransactionType.discount.value,
        TransactionType.sale.value,
        '%$productName%',
      ],
    );

    return maps.map((map) => SalesStatItem.fromMap(map)).toList();
  }

  /// 특정 상품의 판매 기록에서 판매자 정보를 업데이트합니다.
  ///
  /// 상품의 판매자가 변경될 때 호출되어 관련된 모든 판매 기록의 판매자 정보를 동기화합니다.
  ///
  /// [productId]: 상품 ID
  /// [newSellerName]: 새로운 판매자 이름
  /// 반환값: 업데이트된 판매 기록 수
  /// 예외: DB 오류 시 Exception
  ///
  /// 데이터 무결성:
  /// - 트랜잭션으로 원자성 보장
  /// - 상품 ID 기반 정확한 업데이트
  /// - 판매자 정보 일관성 유지
  Future<int> updateSellerNameForProduct(int productId, String newSellerName) async {
    final db = await _database;
    
    try {
      // 트랜잭션으로 데이터 일관성 보장
      return await db.transaction((txn) async {
        final result = await txn.update(
          salesLogTable,
          {'sellerName': newSellerName},
          where: 'productId = ?',
          whereArgs: [productId],
        );
        
        return result;
      });
    } catch (e) {
      throw Exception('판매 기록의 판매자 정보 업데이트 실패: $e');
    }
  }

  /// 특정 상품의 판매 기록에서 판매자 정보를 업데이트합니다 (배치 처리).
  ///
  /// 여러 상품의 판매자 정보를 한 번에 업데이트할 때 사용합니다.
  ///
  /// [productUpdates]: Map<상품ID, 새로운판매자이름>
  /// 반환값: 업데이트된 총 판매 기록 수
  /// 예외: DB 오류 시 Exception
  ///
  /// 성능 최적화:
  /// - 배치 처리로 트랜잭션 효율성 향상
  /// - 메모리 사용량 최적화
  Future<int> updateSellerNamesForProducts(Map<int, String> productUpdates) async {
    final db = await _database;
    
    try {
      return await db.transaction((txn) async {
        int totalUpdated = 0;
        
        for (final entry in productUpdates.entries) {
          final productId = entry.key;
          final newSellerName = entry.value;
          
          final updatedCount = await txn.update(
            salesLogTable,
            {'sellerName': newSellerName},
            where: 'productId = ?',
            whereArgs: [productId],
          );
          
          totalUpdated += updatedCount;
        }
        
        return totalUpdated;
      });
    } catch (e) {
      throw Exception('판매 기록의 판매자 정보 일괄 업데이트 실패: $e');
    }
  }

  /// 특정 상품의 판매 기록에서 상품명을 업데이트합니다.
  ///
  /// [productId]: 상품 ID
  /// [newProductName]: 새로운 상품명
  /// 반환값: 업데이트된 판매 기록 수
  Future<int> updateSalesLogProductName(int productId, String newProductName) async {
    final db = await _database;

    try {
      final updatedCount = await db.update(
        salesLogTable,
        {'productName': newProductName},
        where: 'productId = ?',
        whereArgs: [productId],
      );

      return updatedCount;
    } catch (e) {
      throw Exception('판매 기록의 상품명 업데이트 실패: $e');
    }
  }

  /// 특정 상품의 모든 판매 기록을 조회합니다.
  ///
  /// [productId]: 상품 ID
  /// 반환값: 해당 상품의 판매 기록 목록
  Future<List<SalesLog>> getSalesLogsByProductId(int productId) async {
    final db = await _database;

    try {
      final List<Map<String, dynamic>> maps = await db.query(
        salesLogTable,
        where: 'productId = ?',
        whereArgs: [productId],
        orderBy: 'saleTimestamp DESC',
      );

      return maps.map((map) => SalesLog.fromMap(map)).toList();
    } catch (e) {
      throw Exception('상품별 판매 기록 조회 실패: $e');
    }
  }

  /// ID로 판매 기록 조회
  Future<SalesLog?> getSalesLogById(int salesLogId) async {
    final db = await _database;

    try {
      final List<Map<String, dynamic>> maps = await db.query(
        salesLogTable,
        where: 'id = ?',
        whereArgs: [salesLogId],
        limit: 1,
      );

      if (maps.isEmpty) {
        return null;
      }

      return SalesLog.fromMap(maps.first);
    } catch (e) {
      LoggerUtils.logError('판매 기록 조회 실패: $salesLogId', error: e, tag: 'SalesLogRepository');
      return null;
    }
  }


}
